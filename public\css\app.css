/* Hero Section Colors */
.hero-heading {
    color: #e74c3c !important;
    font-weight: bold;
    letter-spacing: 2px;
    text-transform: uppercase;
}
.hero-title {
    color: #222 !important;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-size: 3rem;
    font-weight: bold;
}
.hero-desc {
    color: #222 !important;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-size: 1.1rem;
}
.hero-btn {
    background: #111;
    color: #fff;
}
.hero-btn:hover {
    background: #ff3c3c;
    color: #fff;
}

/* Navbar Colors */
.navbar, .navbar-nav .nav-link, .navbar-brand {
    color: #111 !important;
}
.navbar-nav .nav-link {
    font-weight: 500;
    color: #111 !important;
}
.navbar-nav .nav-link.active, .navbar-nav .nav-link:hover {
    color: #ff3c3c !important;
    font-weight: bold;
}

/* Additional Custom Styles */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

/* Product Cards */
.product-card {
    overflow: hidden;
    border-radius: 12px;
}

.product-card img {
    transition: transform 0.3s ease;
}

.product-card:hover img {
    transform: scale(1.05);
}

/* Features */
.feature-box {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-5px);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
}

/* Forms */
.form-control {
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
}

/* Cart */
.cart-item {
    border-bottom: 1px solid #eee;
    padding: 15px 0;
}

.cart-item:last-child {
    border-bottom: none;
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }

    .display-5 {
        font-size: 2rem;
    }
}